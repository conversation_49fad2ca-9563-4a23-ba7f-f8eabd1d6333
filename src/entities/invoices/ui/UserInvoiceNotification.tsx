import { Typography } from '@components/typography';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { OLD_APP_ROUTE_NAME, ROUTE_NAMES } from '@config/routes';
import { useUserInvoiceNotification } from '@entities/invoices/hooks';
import { useFeatureToggles } from '@hooks/system';
import ArrowRight from '@icons/arrow-right.svg?react';
import { Link } from '@tanstack/react-router';
import { formatDate } from '@utils/formatters';
import { type FC, type PropsWithChildren, useMemo } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { cn } from '@/shared/utils/tailwind';

import { InvoiceContainerConfigByVariant } from '../config';

export const UserInvoiceNotification: FC = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.common);
  const { data } = useUserInvoiceNotification();

  const invoiceConfig = useMemo(() => {
    if (!data?.state) {
      return null;
    }

    const { nextPaymentDate, state } = data;

    const { text, className } = InvoiceContainerConfigByVariant[state];

    return {
      text,
      nextPaymentDate,
      className,
      state,
    };
  }, [data]);

  if (!invoiceConfig) {
    return null;
  }

  return (
    <UserInvoiceNotificationLink>
      <div
        id="user-invoice-notification"
        className={cn(
          'flex w-full items-center justify-center gap-1.5 px-8 py-2',
          invoiceConfig.className,
        )}
      >
        <Typography className="flex-1 pl-[1.125rem] text-center">
          <Trans
            components={{
              nextPaymentDate: <>{formatDate(invoiceConfig.nextPaymentDate)}</>,
            }}
            i18nKey={invoiceConfig.text}
            t={t}
          />
        </Typography>
        <ArrowRight className="flex-shrink-0" />
      </div>
    </UserInvoiceNotificationLink>
  );
};

function UserInvoiceNotificationLink({ children }: PropsWithChildren) {
  const featureToggles = useFeatureToggles();

  if (featureToggles.invoicesFeature) {
    return (
      <Link to={ROUTE_NAMES.invoices} className="w-full">
        {children}
      </Link>
    );
  }

  return (
    <a className="w-full" href={OLD_APP_ROUTE_NAME.pay}>
      {children}
    </a>
  );
}
