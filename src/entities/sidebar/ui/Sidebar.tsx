import { ROUTE_NAMES } from '@config/routes';
import {
  UserInvoiceState,
  useUserInvoiceNotification,
} from '@entities/invoices';
import { USER_INVOICE_NOTIFICATION_COMPONENT_ID } from '@entities/invoices/constants';
import { useIsMobileView } from '@hooks/system';
import { Link } from '@tanstack/react-router';
import { MAIN_LAYOUT_HEADER_ID } from '@widgets/layouts/main-layout/constants';
import { useUnit } from 'effector-react';
import type { FC, ReactNode } from 'react';
import { useLockBodyScroll } from 'react-use';

import Logo from '@/shared/assets/logo-desktop.svg?react';
import { cn } from '@/shared/utils/tailwind';

import { SIDEBAR_TEST_KEYS } from '../config';
import { sidebarModel } from '../model';
import type { NavLinkType } from '../types';
import { NavMenu } from './NavMenu';

type SidebarProps = {
  navItems: Array<NavLinkType>;
  before?: ReactNode;
  after?: ReactNode;
  className?: string;
};

export const Sidebar: FC<SidebarProps> = ({
  navItems,
  after,
  className,
  before,
}) => {
  const isOpen = useUnit(sidebarModel.store.$isOpen);
  const isMobileView = useIsMobileView();

  const { data: userInvoice } = useUserInvoiceNotification();

  const hasUserInvoice = !!userInvoice?.state;
  const isUserInvoiceOverdue = userInvoice?.state === UserInvoiceState.OVERDUE;

  const getSidebarTopPosition = () => {
    const userInvoiceNotificationComponent = document.getElementById(
      USER_INVOICE_NOTIFICATION_COMPONENT_ID,
    );
    const mainLayoutHeaderComponent = document.getElementById(
      MAIN_LAYOUT_HEADER_ID,
    );

    const mainLayoutHeaderComponentHeight =
      mainLayoutHeaderComponent?.offsetHeight;

    const userInvoiceNotificationComponentHeight =
      userInvoiceNotificationComponent?.offsetHeight;

    if (isOpen && isMobileView && hasUserInvoice && isUserInvoiceOverdue) {
      return `${(userInvoiceNotificationComponentHeight ?? 0) + (mainLayoutHeaderComponentHeight ?? 0)}px`;
    }
    return `${mainLayoutHeaderComponentHeight ?? 0}px`;
  };

  const sidebarTopPosition = getSidebarTopPosition();

  useLockBodyScroll(isOpen && !isMobileView);

  return (
    <aside
      // mobile height equals full height of the screen minus the header height + border
      className={cn(
        `fixed top-[4.0625rem] left-0 z-[5] h-[calc(100vh-4.0625rem)] w-full bg-primary-white duration-300 ease-in-out md:relative md:right-auto md:top-0 md:z-0 md:block md:h-full md:w-[17.5rem] md:translate-y-0 md:border-neutral-200 md:border-r`,
        isOpen ? 'translate-y-0' : '-translate-y-full',
      )}
      style={{
        ...(isOpen && { top: `${sidebarTopPosition}` }),
      }}
      data-testid={SIDEBAR_TEST_KEYS.sidebar}
    >
      <div
        className={cn(
          'sticky top-0 flex h-full flex-col gap-8 overflow-auto p-6 md:gap-14 md:px-10 md:py-12 no-scrollbar',
          className,
        )}
      >
        <Link
          className="pointer-events-none hidden w-max px-2.5 md:pointer-events-auto md:block"
          to={ROUTE_NAMES.dashboard}
        >
          <Logo />
        </Link>
        <div>
          <NavMenu items={navItems} />
          {before}
        </div>
        {after ? (
          <div
            className={cn('mt-auto', isMobileView ? 'pb-[5rem]' : 'pb-0')}
            data-testid={SIDEBAR_TEST_KEYS.bottomSection}
          >
            {after}
          </div>
        ) : null}
      </div>
    </aside>
  );
};
