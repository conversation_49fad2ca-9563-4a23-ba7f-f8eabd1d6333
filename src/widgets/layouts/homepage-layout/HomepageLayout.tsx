import { useIsMobileView } from '@hooks/system';
import { cn } from '@utils/tailwind';
import { HomepageHeader } from '@widgets/headers/homepage-header/ui/HomepageHeader';
import type { PropsWithChildren } from 'react';

import styles from './HomepageLayout.module.css';

export const HomepageLayout = ({ children }: PropsWithChildren) => {
  const isMobileView = useIsMobileView();

  return (
    <div className={styles.container}>
      <HomepageHeader />
      <main
        id="main-homepage-layout-scroll-container"
        className={cn(styles.main, isMobileView && styles.mainMobile)}
      >
        {children}
      </main>
    </div>
  );
};
