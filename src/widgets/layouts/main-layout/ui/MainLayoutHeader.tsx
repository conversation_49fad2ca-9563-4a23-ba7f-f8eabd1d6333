import { ROUTE_NAMES } from '@config/routes';
import { SidebarBurgerButton } from '@features/sidebar-burger-button';
import { Link } from '@tanstack/react-router';

import Logo from '@/shared/assets/logo.svg?react';
import { cn } from '@/shared/utils/tailwind';

import { MAIN_LAYOUT_HEADER_ID } from '../constants';

type MainLayoutHeaderProps = {
  className?: string;
};

export const MainLayoutHeader = ({ className }: MainLayoutHeaderProps) => {
  return (
    <header
      id={MAIN_LAYOUT_HEADER_ID}
      className={cn(
        'z-10 flex items-center justify-between gap-4 border-neutral-200 border-b bg-white px-6 py-3',
        className,
      )}
    >
      <Link to={ROUTE_NAMES.dashboard}>
        <Logo className="shrink-0" />
      </Link>
      <SidebarBurgerButton />
    </header>
  );
};
