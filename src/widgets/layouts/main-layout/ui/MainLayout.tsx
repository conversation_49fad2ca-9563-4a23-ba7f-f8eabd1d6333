import { ROUTE_NAMES } from '@config/routes';
import {
  UserInvoiceState,
  useUserInvoiceNotification,
} from '@entities/invoices';
import { Sidebar } from '@entities/sidebar';
import { sidebarModel } from '@entities/sidebar';
import { LogoutButton } from '@features/auth/logout/ui/LogoutButton';
import { useMainLayout } from '@widgets/layouts/main-layout/hooks';
import { useUnit } from 'effector-react';
import { lazy, type PropsWithChildren } from 'react';
import { useLocation } from 'react-use';

import { cn } from '@/shared/utils/tailwind';

import { MainLayoutFooter } from './MainLayoutFooter';
import { MainLayoutHeader } from './MainLayoutHeader';

const MainLayoutDesktopLanguageSelector = lazy(() =>
  import('./MainLayoutDesktopLanguageSelector').then(
    ({ MainLayoutDesktopLanguageSelector }) => ({
      default: MainLayoutDesktopLanguageSelector,
    }),
  ),
);

const UserInvoiceNotification = lazy(() =>
  import('@entities/invoices').then(({ UserInvoiceNotification }) => ({
    default: UserInvoiceNotification,
  })),
);

export const MainLayout = ({ children }: PropsWithChildren) => {
  const { sidebarNavLinks } = useMainLayout();
  const { data: userInvoice } = useUserInvoiceNotification();
  const { pathname } = useLocation();
  const isOpen = useUnit(sidebarModel.store.$isOpen);

  const isDealPage = pathname?.includes(ROUTE_NAMES.deals);
  const maxWidth = isDealPage ? 'max-w-[100rem]' : 'max-w-[61.25rem]';

  const hasUserInvoice = !!userInvoice?.state;
  const isUserInvoiceOverdue = userInvoice?.state === UserInvoiceState.OVERDUE;

  return (
    <div
      className={cn(
        'relative grid-areas-[header,invoice,content] md:grid-areas-[invoice_invoice,sidebar_content] grid h-full grid-rows-[auto,auto,1fr] md:grid-cols-[auto,1fr] md:grid-rows-[auto,1fr] overflow-auto no-scrollbar',
      )}
    >
      <MainLayoutHeader className="fixed top-0 left-0 right-0 z-30 md:hidden" />

      <div
        className={cn(
          'grid-in-[invoice]',
          isUserInvoiceOverdue && 'sticky top-[65px] z-20 md:top-0',
        )}
      >
        {hasUserInvoice ? <UserInvoiceNotification /> : null}
      </div>

      <Sidebar
        after={<LogoutButton />}
        className="grid-in-[sidebar]"
        navItems={sidebarNavLinks}
        before={
          <div className="pt-14 md:pt-8">
            <MainLayoutDesktopLanguageSelector />
          </div>
        }
      />

      <div
        id="main-layout-scroll-container"
        className={cn(
          'grid-in-[content] no-scrollbar size-full overflow-auto transition-transform duration-300 ease-in-out pt-[4.0625rem] md:pt-0',
          isOpen && 'translate-y-[calc(100vh-4.0625rem)] md:translate-y-0',
        )}
      >
        <div className={cn(maxWidth, 'mx-auto flex size-full flex-col')}>
          <main className="flex-1">{children}</main>
          <MainLayoutFooter />
        </div>
      </div>
    </div>
  );
};
