.homepageHeader {
  @apply w-full;
}

.headerTop {
  @apply w-full bg-primary-black flex flex-col items-center;
}

.headerTopContainer {
  @apply flex justify-between items-center w-full md:max-w-[1600px] px-6 md:px-12;
}

.headerTopLeft {
  @apply flex items-center -ml-4;
}

.headerTopLeftLink {
  @apply text-neutral-400 px-4 h-14 flex items-center hover:text-primary-white/85;
}

.activeLinkPrimary {
  @apply !text-primary-white pointer-events-none;
}

.headerBottom {
  @apply w-full flex flex-col items-center;
}

.headerBottomContainer {
  @apply flex justify-between items-center w-full md:max-w-[1600px] py-3 px-6 md:px-12;
}

.headerBottomCenter {
  @apply flex items-center gap-2.5;
}

.headerBottomLink {
  @apply text-neutral-700 py-2 px-4 flex items-center hover:bg-neutral-50 hover:text-primary-black rounded-md;
}

.activeLinkSecondary {
  @apply !text-primary-black pointer-events-none;
}

.headerBottomRight {
  @apply flex items-center gap-2;
}

.languageSelector {
  @apply text-primary-white [&>p]:font-normal [&>svg]:size-[0.875rem];
}

/* MOBILE */

.homepageHeaderMobile {
  @apply w-full fixed top-0 left-0 z-30;
}

.homepageHeaderMobileMenuContainer {
  @apply flex justify-between items-center bg-primary-white border-solid z-30 relative px-6 py-4 md:px-12;
}

.homepageHeaderMobileMenu {
  @apply fixed inset-0 h-[calc(100vh-3.5625rem)] -translate-y-[100%]  bg-primary-white flex flex-col duration-300 ease-in-out overflow-y-auto no-scrollbar;
}

.homepageHeaderMobileMenuIsOpen {
  @apply translate-y-[4rem];
}

.homepageHeaderMobileMenuAuth {
  @apply flex gap-4 px-6 py-4 border-solid;
}

.homepageHeaderMobileMenuLinks {
  @apply flex flex-col gap-3 py-6;
}

.homepageHeaderMobileMenuLink {
  @apply px-6 h-[2.75rem] flex items-center hover:bg-neutral-50;
}

.languageRowSelector {
  @apply p-4;
}

.separator {
  @apply text-neutral-200 md:opacity-0 relative z-30;
}
