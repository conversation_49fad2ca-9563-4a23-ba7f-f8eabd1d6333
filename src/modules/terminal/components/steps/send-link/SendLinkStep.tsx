import { But<PERSON>, VStack } from '@chakra-ui/react';
import { TerminalStepWrapper } from 'modules/terminal/components/shared';
import {
  useSendLinkStep,
  useSendLinkStepHandlers,
} from 'modules/terminal/components/steps/send-link/SendLinkStep.hooks';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { type Control, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FiCopy, FiExternalLink, FiSend } from 'react-icons/fi';
import { UserMessageSentType } from 'shared/api';
import {
  ControlledToggle,
  PatternNumberInput,
  QrCodeIcon,
  TextInput,
  ToggleItem,
} from 'shared/components';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import { ApplicationLinkAction } from 'shared/types';
import { PHONE_FORMAT } from 'shared/utils';

export const SendLinkStep = () => {
  const { t } = useTranslation(LocizeNamespaces.TERMINAL);
  const {
    isValidTerminalForm,
    isVisible,
    sendLinkTypeOptions,
    stepNo,
    colorScheme,
    handleSubmit,
    control,
    userMessageSentType,
    isValidSendLinkBlock,
  } = useSendLinkStep();

  const {
    handleSendApplicationLink,
    handleCopyApplicationLink,
    submittedAction,
    isLoading,
    handleOpenInNewTab,
    handleGenerateQR,
  } = useSendLinkStepHandlers();

  const additionalActions = [
    {
      action: ApplicationLinkAction.OpenInNewTab,
      icon: <FiExternalLink />,
      onClick: handleOpenInNewTab,
      dataCy: CypressTerminalKeys.SUBMIT_ACTION_BUTTON_OPEN_IN_NEW_TAB,
    },
    {
      action: ApplicationLinkAction.CopyLink,
      icon: <FiCopy />,
      onClick: handleCopyApplicationLink,
      dataCy: CypressTerminalKeys.SUBMIT_ACTION_BUTTON_COPY_LINK,
    },
    {
      action: ApplicationLinkAction.GenerateQR,
      icon: <QrCodeIcon />,
      onClick: handleGenerateQR,
      dataCy: CypressTerminalKeys.SUBMIT_ACTION_BUTTON_GENERATE_QR_CODE,
    },
  ];

  return (
    <TerminalStepWrapper
      dataCy={CypressTerminalKeys.SEND_LINK_STEP}
      isCompleted
      isVisible={isVisible}
      stepNo={stepNo}
      title={t('send-link.title')}
    >
      <ControlledToggle
        control={control as Control<TerminalFormSchemaType>}
        isDisabled={isLoading}
        mb={4}
        name="userMessageSentType"
        w="100%"
      >
        {sendLinkTypeOptions.map(({ value, label, dataCy }) => (
          <ToggleItem
            colorScheme={colorScheme}
            dataCy={dataCy}
            key={value}
            value={value}
          >
            {t(label)}
          </ToggleItem>
        ))}
      </ControlledToggle>
      {userMessageSentType === UserMessageSentType.SMS && (
        <Controller
          control={control}
          name="personalInfoPhone"
          render={({ field: { onChange, ...rest } }) => (
            <PatternNumberInput
              allowEmptyFormatting
              data-cy={CypressTerminalKeys.SEND_LINK_PHONE_NUMBER_INPUT}
              disabled={isLoading}
              format={PHONE_FORMAT}
              label={t('send-link.phone.label')}
              onValueChange={({ value }) => {
                onChange(value);
              }}
              type="tel"
              {...rest}
            />
          )}
        />
      )}
      {userMessageSentType === UserMessageSentType.EMAIL && (
        <Controller
          control={control}
          name="personalInfoEmail"
          render={({ field, fieldState: { error } }) => (
            <TextInput
              data-cy={CypressTerminalKeys.SEND_LINK_EMAIL_INPUT}
              error={
                error?.message ? t(`common:forms.${error.message}`) : undefined
              }
              inputMode="email"
              isDisabled={isLoading}
              label={t('send-link.email.label')}
              {...field}
            />
          )}
        />
      )}
      <Button
        colorScheme={colorScheme}
        data-cy={CypressTerminalKeys.SUBMIT_ACTION_BUTTON_SEND_LINK}
        isDisabled={isLoading || !(isValidTerminalForm && isValidSendLinkBlock)}
        isLoading={
          !!isLoading && submittedAction === ApplicationLinkAction.SendLink
        }
        leftIcon={<FiSend />}
        mb={10}
        mt={6}
        onClick={handleSubmit(handleSendApplicationLink)}
        width="full"
      >
        {t('send-link.actions.send-link')}
      </Button>
      <VStack spacing={0}>
        {additionalActions.map(({ action, icon, onClick, dataCy }) => (
          <Button
            colorScheme={colorScheme}
            data-cy={dataCy}
            isDisabled={isLoading || !isValidTerminalForm}
            isLoading={!!isLoading && submittedAction === action}
            justifyContent="flex-start"
            key={action}
            leftIcon={icon}
            onClick={handleSubmit(onClick)}
            size="sm"
            variant="ghost"
            width="full"
          >
            {t(`send-link.actions.${action}`)}
          </Button>
        ))}
      </VStack>
    </TerminalStepWrapper>
  );
};
