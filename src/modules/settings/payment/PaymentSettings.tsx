import { Text, VStack } from '@chakra-ui/react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationScheduleType, MerchantSettingsBonusType } from 'shared/api';
import { useMerchantDetails } from 'shared/hooks/merchant';
import {
  type PaymentMethodInfo,
  usePaymentMethodInfo,
} from 'shared/hooks/payment-plan';
import { PaymentProvider } from 'shared/utils/app-settings';

import { PaymentMethodListItem } from './PaymentMethodListItem';

type MethodInfo = {
  mainDescription: string;
  secondaryDescription: string;
  isLastEnabled: boolean;
  info: PaymentMethodInfo;
};

type SupportedScheduleType =
  | ApplicationScheduleType.REGULAR
  | ApplicationScheduleType.ESTO_X
  | ApplicationScheduleType.PAY_LATER
  | ApplicationScheduleType.ESTO_PAY;

function useMethods(): Record<SupportedScheduleType, MethodInfo> {
  const { t } = useTranslation('settings');
  const merchatDetails = useMerchantDetails();
  const campaign = merchatDetails?.data?.merchant?.campaign;
  const settings = merchatDetails?.data?.merchant?.settings;

  const partialMethods = useMemo(() => {
    const methods = {} as Record<
      SupportedScheduleType,
      Omit<MethodInfo, 'info'>
    >;

    methods[ApplicationScheduleType.REGULAR] = {
      mainDescription: '',
      secondaryDescription: '',
      isLastEnabled: false,
    };
    methods[ApplicationScheduleType.REGULAR].mainDescription = t(
      `payment-method.${ApplicationScheduleType.REGULAR}.description-main`,
    );
    if ((settings?.reverse_kickback_pct ?? 0) > 0) {
      methods[ApplicationScheduleType.REGULAR].secondaryDescription = t(
        `payment-method.${ApplicationScheduleType.REGULAR}.description-secondary-3`,
        {
          bonus: `${settings?.reverse_kickback_pct ?? 0}€`,
        },
      );
    } else if (
      (settings?.bonus_pct ?? 0) > 0 &&
      settings?.bonus_type !== MerchantSettingsBonusType.NONE
    ) {
      if (settings?.bonus_type === MerchantSettingsBonusType.PRINCIPAL) {
        methods[ApplicationScheduleType.REGULAR].secondaryDescription = t(
          `payment-method.${ApplicationScheduleType.REGULAR}.description-secondary-1`,
          {
            bonus: `${settings?.bonus_pct ?? 0}%`,
          },
        );
      } else {
        methods[ApplicationScheduleType.REGULAR].secondaryDescription = t(
          `payment-method.${ApplicationScheduleType.REGULAR}.description-secondary-2`,
          {
            bonus: `${settings?.bonus_pct ?? 0}%`,
          },
        );
      }
    }
    methods[ApplicationScheduleType.REGULAR].isLastEnabled =
      !!campaign?.regular_hp_enabled &&
      !campaign?.converting_schedule_enabled &&
      !campaign?.pay_later_enabled &&
      !campaign?.esto_pay_enabled;

    methods[ApplicationScheduleType.ESTO_X] = {
      mainDescription: '',
      secondaryDescription: '',
      isLastEnabled: false,
    };
    methods[ApplicationScheduleType.ESTO_X].mainDescription = t(
      `payment-method.${ApplicationScheduleType.ESTO_X}.description-main`,
      {
        period: campaign?.converting_schedule_months ?? 0,
      },
    );
    if ((campaign?.converting_schedule_reverse_kickback_pct ?? 0) > 0) {
      methods[ApplicationScheduleType.ESTO_X].secondaryDescription = t(
        `payment-method.${ApplicationScheduleType.ESTO_X}.description-secondary`,
        {
          feePct: `${campaign?.converting_schedule_reverse_kickback_pct ?? 0}%`,
        },
      );
    }
    methods[ApplicationScheduleType.ESTO_X].isLastEnabled =
      !campaign?.regular_hp_enabled &&
      !!campaign?.converting_schedule_enabled &&
      !campaign?.pay_later_enabled &&
      !campaign?.esto_pay_enabled;

    methods[ApplicationScheduleType.PAY_LATER] = {
      mainDescription: '',
      secondaryDescription: '',
      isLastEnabled: false,
    };
    methods[ApplicationScheduleType.PAY_LATER].mainDescription = t(
      `payment-method.${ApplicationScheduleType.PAY_LATER}.description-main`,
    );
    if ((campaign?.pay_later_reverse_kickback_pct ?? 0) > 0) {
      methods[ApplicationScheduleType.PAY_LATER].secondaryDescription = t(
        `payment-method.${ApplicationScheduleType.PAY_LATER}.description-secondary`,
        {
          feePct: `${campaign?.pay_later_reverse_kickback_pct ?? 0}%`,
        },
      );
    }
    methods[ApplicationScheduleType.PAY_LATER].isLastEnabled =
      !campaign?.regular_hp_enabled &&
      !campaign?.converting_schedule_enabled &&
      !!campaign?.pay_later_enabled &&
      !campaign?.esto_pay_enabled;

    methods[ApplicationScheduleType.ESTO_PAY] = {
      mainDescription: '',
      secondaryDescription: '',
      isLastEnabled: false,
    };
    methods[ApplicationScheduleType.ESTO_PAY].mainDescription = t(
      `payment-method.${ApplicationScheduleType.ESTO_PAY}.description-main`,
    );

    // TODO: Refactor to unhardcode getkevin provider. Providers can be multiple
    if (
      (campaign?.direct_payment_gateways?.find(
        (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
      )?.fee_fixed ?? 0) > 0 ||
      (campaign?.direct_payment_gateways?.find(
        (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
      )?.fee_pct ?? 0) > 0
    ) {
      methods[ApplicationScheduleType.ESTO_PAY].secondaryDescription = t(
        `payment-method.${ApplicationScheduleType.ESTO_PAY}.description-secondary`,
        {
          fixed: `${
            campaign?.direct_payment_gateways?.find(
              (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
            )?.fee_fixed ?? 0
          }€`,
          feePct: `${
            campaign?.direct_payment_gateways?.find(
              (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
            )?.fee_pct ?? 0
          }%`,
          min: `${
            campaign?.direct_payment_gateways?.find(
              (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
            )?.fee_total_min ?? 0
          }€`,
          max: `${
            campaign?.direct_payment_gateways?.find(
              (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
            )?.fee_total_max ?? 0
          }€`,
        },
      );
    }
    methods[ApplicationScheduleType.ESTO_PAY].isLastEnabled =
      !campaign?.regular_hp_enabled &&
      !campaign?.converting_schedule_enabled &&
      !campaign?.pay_later_enabled &&
      !!campaign?.esto_pay_enabled;

    return methods;
  }, [campaign, settings, t]);

  // Add the info property to complete the MethodInfo type
  const completeMethods: Record<SupportedScheduleType, MethodInfo> = {
    [ApplicationScheduleType.REGULAR]: {
      ...partialMethods[ApplicationScheduleType.REGULAR],
      info: usePaymentMethodInfo(ApplicationScheduleType.REGULAR),
    },
    [ApplicationScheduleType.ESTO_X]: {
      ...partialMethods[ApplicationScheduleType.ESTO_X],
      info: usePaymentMethodInfo(ApplicationScheduleType.ESTO_X),
    },
    [ApplicationScheduleType.PAY_LATER]: {
      ...partialMethods[ApplicationScheduleType.PAY_LATER],
      info: usePaymentMethodInfo(ApplicationScheduleType.PAY_LATER),
    },
    [ApplicationScheduleType.ESTO_PAY]: {
      ...partialMethods[ApplicationScheduleType.ESTO_PAY],
      info: usePaymentMethodInfo(ApplicationScheduleType.ESTO_PAY),
    },
  };

  return completeMethods;
}

export const PaymentSettings = () => {
  const { t } = useTranslation('settings');
  const methods = useMethods();

  return (
    <>
      <Text display={['none', null, 'block']} mb={2} textStyle="h4">
        {t('payment-method.title')}
      </Text>
      <Text mb={4} textStyle="body2">
        {t('payment-method.name-logo-note')}
      </Text>
      <VStack mb={10} spacing={[3, 4]}>
        {(Object.keys(methods) as SupportedScheduleType[]).map(
          (method: SupportedScheduleType) => {
            const methodDetails = methods[method];

            return (
              <PaymentMethodListItem
                key={method}
                mainDescription={methodDetails.mainDescription}
                method={method}
                methodInfo={methodDetails.info}
                secondaryDescription={methodDetails.secondaryDescription}
                shouldDisable
              />
            );
          },
        )}
      </VStack>
    </>
  );
};
