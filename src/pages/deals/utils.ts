import { LOCIZE_DEALS_KEYS } from '@config/locize';

export const isCategoryWithSeoData = (
  categoryName: string | undefined,
): categoryName is keyof typeof LOCIZE_DEALS_KEYS.category => {
  return Boolean(categoryName && categoryName in LOCIZE_DEALS_KEYS.category);
};

type ScrollToCategoryOptions = {
  delay?: number;
  desktopBlock?: ScrollLogicalPosition;
  isMobileView?: boolean;
};

export const scrollToCategory = (
  category: string,
  options: ScrollToCategoryOptions = {},
) => {
  const { delay = 100, desktopBlock = 'start', isMobileView = false } = options;

  setTimeout(() => {
    if (isMobileView) {
      const container =
        document.getElementById('main-homepage-layout-scroll-container') ||
        document.getElementById('main-layout-scroll-container');

      if (container) {
        container.scrollTo({
          top: 0,
        });
      }
    } else {
      const element = document.querySelector(`[data-category="${category}"]`);

      if (element) {
        element.scrollIntoView({ block: desktopBlock });
      }
    }
  }, delay);
};
