import type { SeoData } from '@components/SeoHelmet';
import { SeoHelmet } from '@components/SeoHelmet';
import { Typography } from '@components/typography';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useGetDeals } from '@entities/deals/hooks/useGetDeals';
import { useIsMobileView } from '@hooks/system';
import { getRouteApi } from '@tanstack/react-router';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  getDealCategoryIcon,
  getDealCategoryTranslationKey,
} from '../../config';
import { isCategoryWithSeoData } from '../../utils';
import { DealsNoResult } from '../deals-no-results-section/DealsNoResults';
import { DealsList } from '../DealsList';

const routeApi = getRouteApi('/_protected/_main/deals');

export const DealsCategorySection = ({ className }: { className?: string }) => {
  const { title, category } = routeApi.useSearch();
  const { data } = useGetDeals({ title, categories: category });
  const isMobileView = useIsMobileView();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const seoData: SeoData = useMemo(() => {
    if (!isCategoryWithSeoData(category)) {
      return null;
    }

    const categoryData = LOCIZE_DEALS_KEYS.category[category];

    return {
      title: t(categoryData.meta.title),
      description: t(categoryData.meta.description),
      keywords: t(categoryData.meta.keywords),
    };
  }, [category, t]);

  const CategoryIcon = useMemo(() => {
    return getDealCategoryIcon(category || '');
  }, [category]);

  const mobileCategoryHeader = useMemo(() => {
    if (!isMobileView || !category) return null;

    const translationKey = getDealCategoryTranslationKey(category);
    const categoryName = t(translationKey);

    return (
      <div className="mb-6 flex items-center gap-2">
        <CategoryIcon className="h-5 w-5" />
        <Typography variant="xxs" affects="semibold">
          {categoryName}
        </Typography>
      </div>
    );
  }, [isMobileView, category, CategoryIcon]);

  if (!data) {
    return (
      <>
        <SeoHelmet seoData={seoData} />
        <div className={className}>
          <DealsNoResult />
        </div>
      </>
    );
  }

  return (
    <>
      <SeoHelmet seoData={seoData} />
      <div className={className}>
        {mobileCategoryHeader}
        <DealsList data={data} />
      </div>
    </>
  );
};
