import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { useGetCategories } from '@entities/deals/hooks/useGetCategories';
import {
  getDealCategoryTranslationKey,
  isDealCategory,
} from '@pages/deals/config';
import { Suspense } from 'react';
import { useTranslation } from 'react-i18next';

import { DealsCategoryMainSectionCarousel } from './DealsCategoryMainSectionCarousel';
import FeaturedDealsList from './FeaturedDealsList';

const DealMainSection = () => {
  const { data: categories } = useGetCategories();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  if (!categories) {
    return null;
  }

  return (
    <div className={'mt-[2.5rem]'}>
      <FeaturedDealsList />
      {categories?.map((category) => {
        if (!category) {
          return null;
        }

        if (!isDealCategory(category.name)) {
          return null;
        }

        const categoryName = category.name;
        const translationKey = getDealCategoryTranslationKey(categoryName);
        const title = t(translationKey);

        return (
          <Suspense
            key={categoryName}
            fallback={
              <Skeleton className="mx-6 mb-10 h-[24rem] rounded-2xl md:mx-12" />
            }
          >
            <DealsCategoryMainSectionCarousel
              category={categoryName}
              title={title}
            />
          </Suspense>
        );
      })}
    </div>
  );
};

export default DealMainSection;
