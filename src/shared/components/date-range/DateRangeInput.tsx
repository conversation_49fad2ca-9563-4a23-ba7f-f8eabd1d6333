import {
  FormErrorIcon,
  FormErrorMessage,
  Icon,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  useMultiStyleConfig,
} from '@chakra-ui/react';
import { forwardRef } from 'react';
import { FiAlertCircle, FiCalendar } from 'react-icons/fi';

import { type BaseInputProps, InputWrapper } from '../controls/common';

type DateRangeInputProps = {
  isOpen?: boolean;
  value: string;
} & BaseInputProps;

export const DateRangeInput = forwardRef<HTMLInputElement, DateRangeInputProps>(
  ({ isOpen, value, label, error, isDisabled, ...chakraProps }, ref) => {
    const styles = useMultiStyleConfig('Input', {});
    const focusedStyles = {
      ...(styles.field as Record<string, unknown>)?._focus,
      pl: 'calc(3rem - 1px)',
      pr: error ? 'calc(3rem - 1px)' : undefined,
    };

    return (
      <InputWrapper
        error={error}
        isDisabled={isDisabled}
        label={label}
        {...chakraProps}
      >
        <InputGroup>
          <InputLeftElement width={12}>
            <Icon as={FiCalendar} boxSize={6} color="primary.800" />
          </InputLeftElement>
          <Input
            _focus={focusedStyles}
            cursor="pointer"
            pl={12}
            pr={error ? 12 : undefined}
            ref={ref}
            value={value}
            {...(isOpen ? focusedStyles : {})}
            _selection={{ color: 'inherit' }}
            readOnly
          />

          {!!error && (
            <InputRightElement width={12}>
              <FormErrorMessage>
                <FormErrorIcon as={FiAlertCircle} boxSize={6} color="red.700" />
              </FormErrorMessage>
            </InputRightElement>
          )}
        </InputGroup>
      </InputWrapper>
    );
  },
);
DateRangeInput.displayName = 'DateRangeInput';
