import { Input } from '@components/ui/input';
import { cn } from '@utils/tailwind';
import { Search, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

type SearchInputProps = {
  placeholder: string;
  className?: string;
  onClick?: () => void;
  onClear?: () => void;
  value: string;
  onChange?: (value: string) => void;
  autoFocus?: boolean;
};
export interface SearchInputRef {
  clear: () => void;
}
export const SearchInput = ({
  onClick,
  placeholder,
  className = '',
  onClear,
  value,
  onChange,
  autoFocus = false,
}: SearchInputProps) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const shouldShowClear = onClear && (value || isFocused);

  return (
    <div className={`relative rounded-full ${className}`}>
      <Search className="-translate-y-1/2 absolute top-1/2 left-4 h-5 w-5 text-primary-black" />
      <Input
        ref={inputRef}
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        onClick={() => onClick?.()}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={cn(
          'h-12 rounded-full border-0 bg-transparent pl-12 text-base focus-visible:ring-0 focus-visible:ring-offset-0',
          value && 'font-semibold',
        )}
      />
      {shouldShowClear && (
        <button
          type="button"
          onClick={() => {
            onClear?.();
          }}
          className="-translate-y-1/2 absolute top-1/2 right-4 rounded-full p-1 text-primary-black hover:bg-neutral-200 hover:text-gray-700 "
          aria-label="Clear search"
        >
          <X className="h-6 w-6" />
        </button>
      )}
    </div>
  );
};
