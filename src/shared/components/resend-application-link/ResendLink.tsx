import { But<PERSON>, Spinner, useDisclosure } from '@chakra-ui/react';
import copy from 'copy-to-clipboard';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FiCopy, FiExternalLink, FiSend } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { useHandleGenericError, useShowMessage } from 'shared/hooks/alerts';
import { ApplicationLinkAction } from 'shared/types';

import { QrCodeIcon } from '../icons';
import { QrCodeModal } from './QrCodeModal';
import { SendLinkModal } from './SendLinkModal';

type ActionButtonProps = {
  icon: JSX.Element;
  action: ApplicationLinkAction;
  pageAction: ApplicationLinkAction;
  onAction: () => Promise<void>;
  dataCy: string;
  isPracticeMode: boolean;
};

const ActionButton = ({
  action,
  onAction,
  pageAction,
  icon,
  dataCy,
  isPracticeMode,
}: ActionButtonProps) => {
  const { t } = useTranslation('terminal');
  const handleGenericError = useHandleGenericError();
  const [isActionInProgress, setActionInProgress] = useState(false);
  const onClick = useCallback(async () => {
    setActionInProgress(true);

    try {
      await onAction();
    } catch (e) {
      handleGenericError(e);
    }

    setActionInProgress(false);
  }, [onAction, handleGenericError]);

  return (
    <Button
      color={isPracticeMode ? 'neutral.800' : 'primary.800'}
      data-cy={dataCy}
      justifyContent="flex-start"
      leftIcon={
        isActionInProgress ? (
          <Spinner
            color="primary.800"
            emptyColor="transparent"
            thickness="2px"
            verticalAlign="middle"
          />
        ) : (
          icon
        )
      }
      onClick={onClick}
      size="sm"
      sx={{
        whiteSpace: 'wrap',
        textAlign: 'left',
      }}
      variant="ghost"
      width="full"
    >
      {pageAction === action
        ? t(`application-status.resend-action.${action}`)
        : t(`send-link.actions.${action}`)}
    </Button>
  );
};

export type ResendLinkBlockProps = {
  action?: ApplicationLinkAction;
  applicationId: number;
  purchaseUrl: string;
  isPracticeMode?: boolean;
  qrCodeRedirect?: string;
  isFromRetail?: boolean;
};

export const ResendLinkBlock = ({
  action = ApplicationLinkAction.SendLink,
  purchaseUrl,
  applicationId,
  isPracticeMode = false,
  qrCodeRedirect,
  isFromRetail = true,
}: ResendLinkBlockProps): JSX.Element => {
  const { t } = useTranslation('terminal');
  const sendLinkDisclosure = useDisclosure();
  const qrCodeDisclosure = useDisclosure();

  const showMessage = useShowMessage();
  const navigate = useNavigate();

  const onSendLinkSuccess = useCallback(
    async (destination: string) => {
      sendLinkDisclosure.onClose();
      showMessage(t('notifications.link-sent', { destination }), FiSend);
    },
    [t, showMessage, sendLinkDisclosure],
  );

  const onCopyLink = useCallback(async () => {
    copy(purchaseUrl);
    showMessage(t('notifications.link-copied') || '', FiCopy);
  }, [purchaseUrl, t, showMessage]);

  const onOpenInNewTab = useCallback(async () => {
    window.open(purchaseUrl, '_blank');
  }, [purchaseUrl]);

  const onQrCode = useCallback(async () => {
    if (qrCodeRedirect) {
      navigate(qrCodeRedirect, {
        replace: true,
      });
    } else {
      qrCodeDisclosure.onOpen();
    }
  }, [navigate, qrCodeRedirect, qrCodeDisclosure]);

  return (
    <>
      <ActionButton
        action={ApplicationLinkAction.SendLink}
        dataCy="actions-send-link"
        icon={<FiSend />}
        isPracticeMode={isPracticeMode}
        onAction={async () => {
          sendLinkDisclosure.onOpen();
        }}
        pageAction={action}
      />
      <ActionButton
        action={ApplicationLinkAction.CopyLink}
        dataCy="actions-copy-link"
        icon={<FiCopy />}
        isPracticeMode={isPracticeMode}
        onAction={onCopyLink}
        pageAction={action}
      />
      {!!isFromRetail && (
        <ActionButton
          action={ApplicationLinkAction.OpenInNewTab}
          dataCy="actions-open-in-new-tab"
          icon={<FiExternalLink />}
          isPracticeMode={isPracticeMode}
          onAction={onOpenInNewTab}
          pageAction={action}
        />
      )}
      {action !== ApplicationLinkAction.GenerateQR && (
        <ActionButton
          action={ApplicationLinkAction.GenerateQR}
          dataCy="actions-qr-code"
          icon={<QrCodeIcon />}
          isPracticeMode={isPracticeMode}
          onAction={onQrCode}
          pageAction={action}
        />
      )}
      <SendLinkModal
        applicationId={applicationId}
        isOpen={sendLinkDisclosure.isOpen}
        isResend={action === ApplicationLinkAction.SendLink}
        onClose={sendLinkDisclosure.onClose}
        onSuccess={onSendLinkSuccess}
      />
      <QrCodeModal
        isOpen={qrCodeDisclosure.isOpen}
        onClose={qrCodeDisclosure.onClose}
        purchaseUrl={purchaseUrl}
      />
    </>
  );
};
