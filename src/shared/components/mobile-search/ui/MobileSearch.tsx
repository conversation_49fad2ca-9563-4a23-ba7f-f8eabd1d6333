import { Button } from '@components/ui/button';
import { SearchInput } from '@components/ui/search';
import { ArrowLeft } from 'lucide-react';

type MobileSearchProps = {
  placeholder: string;
  children?: React.ReactNode;
  onChange: (title: string) => void;
  onCloseSearch: () => void;
  onClear: () => void;
  value: string;
};

export const MobileSearch = ({
  onCloseSearch,
  placeholder,
  children,
  onChange,
  onClear,
  value,
}: MobileSearchProps) => {
  return (
    <div className="fixed inset-0 z-30 w-full bg-white">
      <div className="flex items-center gap-3 px-4 pt-4">
        <div className="-translate-x-[60px] animate-slide-in-left opacity-0">
          <Button
            variant="white"
            size="small"
            onClick={onCloseSearch}
            className="rounded-full bg-white"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </div>

        <div className="-translate-x-10 flex-1 animate-slide-in-right opacity-0">
          <SearchInput
            autoFocus
            onChange={onChange}
            value={value}
            className="mx-auto w-full rounded-full border border-gray-200 bg-white"
            placeholder={placeholder}
            onClear={onClear}
          />
        </div>
      </div>
      {children && children}
    </div>
  );
};
