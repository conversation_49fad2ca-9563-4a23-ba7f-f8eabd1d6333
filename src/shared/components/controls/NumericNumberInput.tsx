import {
  FormErrorIcon,
  FormErrorMessage,
  Input,
  InputGroup,
  InputRightElement,
  useMultiStyleConfig,
} from '@chakra-ui/react';
import { forwardRef, type ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { FiAlertCircle } from 'react-icons/fi';
import { NumericFormat, type NumericFormatProps } from 'react-number-format';
import { getDecimalSeparator } from 'shared/utils';

import { InputWrapper } from './common';

type Props = {
  label?: string | ReactNode;
  hint?: string;
  error?: string;
} & Omit<NumericFormatProps, 'customInput' | 'size'>;

export const NumericNumberInput = forwardRef<HTMLInputElement, Props>(
  ({ label, name, error, hint, disabled, decimalScale = 2, ...rest }, ref) => {
    const styles = useMultiStyleConfig('Input', {});
    const { i18n } = useTranslation();

    return (
      <InputWrapper
        error={error}
        hint={hint}
        isDisabled={disabled}
        label={label}
        name={name}
      >
        <InputGroup>
          <NumericFormat
            _focus={{
              ...((styles.field as Record<string, unknown>)?._focus as object),
              pr: error ? 'calc(3rem - 1px)' : undefined,
            }}
            customInput={Input}
            decimalScale={decimalScale}
            decimalSeparator={getDecimalSeparator(i18n.language)}
            disabled={disabled}
            getInputRef={ref}
            name={name}
            style={{
              paddingRight: error ? 12 : undefined,
            }}
            {...rest}
          />

          <InputRightElement width={12}>
            <FormErrorMessage>
              <FormErrorIcon as={FiAlertCircle} boxSize={6} color="red.700" />
            </FormErrorMessage>
          </InputRightElement>
        </InputGroup>
      </InputWrapper>
    );
  },
);

NumericNumberInput.displayName = 'NumericNumberInput';
