import i18n from 'i18next';
import { region, zendeskChatKeyCode } from 'shared/lib/env';
import type { IObjectKeysStrings } from 'shared/types';
import { AppRegions } from 'shared/utils';

const SELECT_DEPARTMENTS_MAP: IObjectKeysStrings = {
  et: 'EE',
  lt: 'LT',
  lv: 'LV',
};

export const changeZendeskLocale = (locale: string): void => {
  if (!zendeskChatKeyCode) return;
  (window as { zE: (...args: unknown[]) => void }).zE(
    'webWidget',
    'setLocale',
    locale,
  );
};

export const setZendeskChatSelectSettings = (): void => {
  (window as { zE: (...args: unknown[]) => void }).zE(
    'webWidget',
    'updateSettings',
    {
      webWidget: {
        chat: {
          departments: {
            select: SELECT_DEPARTMENTS_MAP[region || AppRegions.LV],
          },
        },
      },
    },
  );
};

export const loadZendeskWidget = (locale = i18n.language): void => {
  if (!zendeskChatKeyCode) return;

  const script = document.createElement('script');
  script.type = 'text/javascript';
  script.id = 'ze-snippet';
  script.async = true;
  script.src = `https://static.zdassets.com/ekr/snippet.js?key=${zendeskChatKeyCode}`;
  script.addEventListener('load', () => {
    changeZendeskLocale(locale);
    setZendeskChatSelectSettings();
  });
  document.getElementsByTagName('body')[0].appendChild(script);
};
