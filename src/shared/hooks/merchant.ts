import {
  type ApolloC<PERSON>,
  type QueryHookOptions,
  type Reference,
  useQuery,
} from '@apollo/client';
import { useCallback, useContext, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import {
  ADMIN_MERCHANTS_QUERY,
  type AdminMerchantsQuery,
  type AdminMerchantsQueryVariables,
  type ApplicationScheduleType,
  type CreateMerchantStoreMutation,
  type CreateMerchantStoreMutationVariables,
  type DeleteMerchantStoreMutationVariables,
  type DetachUserMerchantMutationVariables,
  type GenerateSecretKeyMutationVariables,
  MerchantDocument,
  type MerchantFragment,
  type MerchantQuery,
  type MerchantQueryVariables,
  MerchantStoreBasicFragmentDoc,
  type SendMerchantUserInviteMutationVariables,
  type UpdateCalculatorSkinMutationVariables,
  type UpdateMerchantMutationVariables,
  type UpdateMerchantStoreMutationVariables,
  type UpdateMerchantUserPermissionsMutationVariables,
  useCreateMerchantStoreMutation,
  useDeleteMerchantStoreMutation,
  useDetachUserMerchantMutation,
  useGenerateSecretKeyMutation,
  useMerchantInviteQuery,
  useMerchantQuery,
  usePricingQuery,
  UserDataDocument,
  useSendMerchantUserInviteMutation,
  useUpdateCalculatorSkinMutation,
  useUpdateMerchantMutation,
  useUpdateMerchantPaymentMethodsMutation,
  useUpdateMerchantStoreMutation,
  useUpdateMerchantUserPermissionsMutation,
} from 'shared/api';
import { useCurrentUser } from 'shared/hooks/user';
import type { MutationConfig } from 'shared/types';
import { extractValidationErrors } from 'shared/utils';

import { useHandleGenericError } from './alerts';
import { GlobalStateContext } from './state';

export const useAssignedMerchants = () => {
  const { user } = useCurrentUser();

  return useMemo(
    () =>
      user?.merchants?.filter(
        (merchant): merchant is MerchantFragment => !!merchant,
      ) || [],
    [user?.merchants],
  );
};

export const useMerchantDetails = (
  baseOptions?: QueryHookOptions<MerchantQuery, MerchantQueryVariables>,
) => {
  const { merchantId } = useContext(GlobalStateContext);
  return useMerchantQuery({
    skip: !merchantId,
    variables: { merchantId: merchantId ?? -1 },
    ...baseOptions,
  });
};

export const useAdminMerchants = (
  search: string,
  options?: Pick<QueryHookOptions, 'skip'>,
) => {
  const { loading, data } = useQuery<
    AdminMerchantsQuery,
    AdminMerchantsQueryVariables
  >(ADMIN_MERCHANTS_QUERY, {
    ...options,
    variables: { name: search, limit: 20 },
  });
  return {
    isLoading: loading,
    merchants: data?.merchants?.data,
  };
};

type Store = NonNullOrUndefined<
  ArrayElement<NonNullOrUndefined<MerchantQuery['merchant']>['stores']>
>;

export const useAvailableStores = () => {
  // get stores info for selected merchant
  const { loading: isLoading, data } = useMerchantDetails();
  const availableStores = useMemo(
    () => data?.merchant?.stores?.filter((store): store is Store => !!store),
    [data?.merchant?.stores],
  );

  return { isLoading, availableStores };
};

type User = NonNullOrUndefined<
  ArrayElement<NonNullOrUndefined<MerchantQuery['merchant']>['users']>
>;

export const useMerchantUsers = () => {
  // get stores info for selected merchant
  const { loading: isLoading, data } = useMerchantDetails();
  const users = useMemo(
    () => data?.merchant?.users?.filter((user): user is User => !!user),
    [data?.merchant?.users],
  );

  return { isLoading, users };
};

export function useMerchantSettings(): {
  isLoading: boolean;
  settings: NonNullable<MerchantQuery['merchant']>['settings'] | null;
} {
  const { loading: isLoading, data } = useMerchantDetails();
  return { isLoading, settings: data?.merchant?.settings ?? null };
}

export function useUpdateMerchant({ onFieldValidationError }: MutationConfig) {
  const { merchantId } = useContext(GlobalStateContext);
  const [updateMerchantMutation, { loading }] = useUpdateMerchantMutation();
  const handleGenericError = useHandleGenericError();

  const updateMerchant = useCallback(
    async (variables: Omit<UpdateMerchantMutationVariables, 'merchantId'>) => {
      if (!merchantId) {
        return null;
      }
      try {
        return await updateMerchantMutation({
          variables: { ...variables, merchantId },
          // to update merchant data in user.merchants list
          refetchQueries: [{ query: UserDataDocument }],
          awaitRefetchQueries: true,
        });
      } catch (e) {
        const [validationErrors, newError] = extractValidationErrors(e);
        if (Object.keys(validationErrors).length > 0) {
          for (const field of Object.keys(validationErrors)) {
            onFieldValidationError?.(field);
          }
        } else {
          handleGenericError(newError);
        }
        return null;
      }
    },
    [
      handleGenericError,
      updateMerchantMutation,
      merchantId,
      onFieldValidationError,
    ],
  );

  return { isLoading: loading, updateMerchant };
}

export function useDeleteMerchantStore() {
  const [deleteStoreMutation, { loading }] = useDeleteMerchantStoreMutation();
  const handleGenericError = useHandleGenericError();

  const deleteStore = useCallback(
    async (
      variables: DeleteMerchantStoreMutationVariables,
    ): Promise<boolean> => {
      try {
        const result = await deleteStoreMutation({
          variables,
          update(cache, { data }) {
            if (!data?.deleted) {
              return;
            }
            cache.evict({
              id: cache.identify({
                id: variables.storeId,
                __typename: 'MerchantStore',
              }),
            });
            cache.gc();
          },
        });
        return !!result.data?.deleted;
      } catch (e) {
        handleGenericError(e);
        return false;
      }
    },
    [handleGenericError, deleteStoreMutation],
  );

  return { isLoading: loading, deleteStore };
}

function addMerchantStoreToCache(
  cache: ApolloCache<unknown>,
  merchantId: number,
  newStore: CreateMerchantStoreMutation['store'],
): void {
  cache.modify({
    id: cache.identify({ id: merchantId, __typename: 'Merchant' }),
    fields: {
      stores(existingStoreRefs = [], { readField }) {
        const newStoreRef = cache.writeFragment({
          data: newStore,
          fragment: MerchantStoreBasicFragmentDoc,
        });
        if (
          existingStoreRefs.some(
            (ref: Reference) => readField('id', ref) === newStore?.id,
          )
        ) {
          return existingStoreRefs;
        }

        return [...existingStoreRefs, newStoreRef].sort((a, b) => {
          const aName: string = readField('name', a) ?? '';
          const bName: string = readField('name', b) ?? '';
          return aName.localeCompare(bName);
        });
      },
    },
  });
}

export function useCreateMerchantStore() {
  const { merchantId } = useContext(GlobalStateContext);
  const [createStoreMutation, { loading }] = useCreateMerchantStoreMutation();
  const handleGenericError = useHandleGenericError();

  const createStore = useCallback(
    async (
      variables: Omit<CreateMerchantStoreMutationVariables, 'merchantId'>,
    ) => {
      if (!merchantId) {
        return null;
      }

      try {
        return await createStoreMutation({
          variables: { merchantId, ...variables },
          update(cache, { data }) {
            if (data?.store) {
              addMerchantStoreToCache(cache, merchantId, data.store);
            }
          },
        });
      } catch (e) {
        handleGenericError(e);
        return null;
      }
    },
    [handleGenericError, createStoreMutation, merchantId],
  );

  return { isLoading: loading, createStore };
}

export function useUpdateMerchantStore() {
  const [updateStoreMutation, { loading }] = useUpdateMerchantStoreMutation();
  const handleGenericError = useHandleGenericError();

  const updateStore = useCallback(
    async (variables: UpdateMerchantStoreMutationVariables) => {
      try {
        return await updateStoreMutation({
          variables,
        });
      } catch (e) {
        handleGenericError(e);
        return null;
      }
    },
    [handleGenericError, updateStoreMutation],
  );

  return { isLoading: loading, updateStore };
}

export function useUpdateMerchantUserPermissions() {
  const { merchantId } = useContext(GlobalStateContext);
  const { user } = useCurrentUser();
  const [updatePermissionsMutation, { loading }] =
    useUpdateMerchantUserPermissionsMutation();
  const handleGenericError = useHandleGenericError();

  const updatePermissions = useCallback(
    async (
      variables: Omit<
        UpdateMerchantUserPermissionsMutationVariables,
        'merchantId'
      >,
    ) => {
      if (!merchantId || !user) {
        return;
      }

      try {
        return await updatePermissionsMutation({
          variables: { merchantId, ...variables },
          update(cache) {
            cache.modify({
              id: cache.identify({
                id: variables.userId,
                __typename: 'MerchantUser',
              }),
              fields: {
                merchant_permission_bits() {
                  return variables.permissionBits;
                },
              },
            });
            if (user.id === variables.userId) {
              cache.modify({
                id: cache.identify({
                  id: merchantId,
                  __typename: 'UserMerchant',
                }),
                fields: {
                  merchant_permission_bits() {
                    return variables.permissionBits;
                  },
                },
              });
            }
          },
        });
      } catch (e) {
        handleGenericError(e);
        return null;
      }
    },
    [handleGenericError, updatePermissionsMutation, merchantId, user],
  );

  return { isLoading: loading, updatePermissions };
}

export function useDetachMerchantUser() {
  const { merchantId } = useContext(GlobalStateContext);
  const [detachMerchantUserMutation, { loading }] =
    useDetachUserMerchantMutation();
  const handleGenericError = useHandleGenericError();

  const detachMerchantUser = useCallback(
    async (
      variables: Omit<DetachUserMerchantMutationVariables, 'merchantId'>,
    ) => {
      if (!merchantId) {
        return;
      }

      try {
        return await detachMerchantUserMutation({
          variables: { merchantId, ...variables },
          update(cache, { data }) {
            if (!data?.handle_user_merchant) {
              return;
            }
            cache.modify({
              id: cache.identify({ id: merchantId, __typename: 'Merchant' }),
              fields: {
                users(existingUserRefs = [], { readField }) {
                  return existingUserRefs.filter(
                    (ref: Reference) =>
                      readField('id', ref) !== variables.userId,
                  );
                },
              },
            });
          },
        });
      } catch (e) {
        handleGenericError(e);
        return null;
      }
    },
    [handleGenericError, detachMerchantUserMutation, merchantId],
  );

  return { isLoading: loading, detachMerchantUser };
}

export function useUpdatePaymentMethods() {
  const { merchantId } = useContext(GlobalStateContext);
  const [updatePaymentMethodsMutation, { loading }] =
    useUpdateMerchantPaymentMethodsMutation();

  const merchantDetails = useMerchantDetails();
  const campaign = merchantDetails.data?.merchant?.campaign;

  const updateMethods = useCallback(
    async (update: Record<ApplicationScheduleType, boolean>) => {
      if (!merchantId) {
        throw new Error('Merchant id is empty');
      }

      return await updatePaymentMethodsMutation({
        variables: {
          merchantId,
          regularHpEnalbed:
            update.REGULAR ?? campaign?.regular_hp_enabled ?? false,
          convertingScheduleEnabled:
            update.ESTO_X ?? campaign?.converting_schedule_enabled ?? false,
          payLaterEnabled:
            update.PAY_LATER ?? campaign?.pay_later_enabled ?? false,
          estoPayEnabled:
            update.ESTO_PAY ?? campaign?.esto_pay_enabled ?? false,
        },
        update(cache, { data }) {
          if (!data?.campaign) {
            return;
          }
          cache.modify({
            id: cache.identify({ id: merchantId, __typename: 'Merchant' }),
            fields: {
              // @ts-expect-error - Apollo cache field function type mismatch but works correctly
              campaign() {
                return data.campaign;
              },
            },
          });
        },
      });
    },
    [merchantId, campaign, updatePaymentMethodsMutation],
  );

  return { isLoading: loading, updateMethods };
}

export function useSendMerchantUserInvite({
  onFieldValidationError,
}: MutationConfig) {
  const { merchantId } = useContext(GlobalStateContext);
  const [sendMerchantUserInviteMutation, { loading }] =
    useSendMerchantUserInviteMutation();
  const handleGenericError = useHandleGenericError();

  const sendMerchantUserInvite = useCallback(
    async (
      variables: Omit<SendMerchantUserInviteMutationVariables, 'merchantId'>,
    ) => {
      if (!merchantId) {
        return null;
      }
      try {
        return await sendMerchantUserInviteMutation({
          variables: { ...variables, merchantId },
        });
      } catch (e) {
        const [validationErrors, newError] = extractValidationErrors(e);
        if (Object.keys(validationErrors).length > 0) {
          for (const field of Object.keys(validationErrors)) {
            onFieldValidationError?.(field);
          }
        } else {
          handleGenericError(newError);
        }
        return null;
      }
    },
    [
      handleGenericError,
      sendMerchantUserInviteMutation,
      merchantId,
      onFieldValidationError,
    ],
  );

  return { isLoading: loading, sendMerchantUserInvite };
}

export function useUpdateMerchantCalculatorSkin() {
  const [updateCalculatorSkinMutation, { loading }] =
    useUpdateCalculatorSkinMutation();
  const handleGenericError = useHandleGenericError();
  const { merchantId } = useContext(GlobalStateContext);

  const updateCalculatorSkin = useCallback(
    async (variables: UpdateCalculatorSkinMutationVariables) => {
      if (!merchantId) {
        return null;
      }
      try {
        return await updateCalculatorSkinMutation({
          variables,
          // to update merchant color_skin on DeveloperTools page
          refetchQueries: [
            { query: MerchantDocument, variables: { merchantId } },
          ],
          awaitRefetchQueries: true,
        });
      } catch (e) {
        handleGenericError(e);
        return null;
      }
    },
    [handleGenericError, updateCalculatorSkinMutation, merchantId],
  );

  return { isLoading: loading, updateCalculatorSkin };
}

export function useGenerateSecretKey() {
  const [generateSecretKeyMutation, { loading }] =
    useGenerateSecretKeyMutation();
  const handleGenericError = useHandleGenericError();

  const generateSecretKey = useCallback(
    async (variables: GenerateSecretKeyMutationVariables) => {
      try {
        return await generateSecretKeyMutation({
          variables,
          // to update merchant secret_api_key in DeveloperTools page
          refetchQueries: [{ query: MerchantDocument, variables }],
          awaitRefetchQueries: true,
        });
      } catch (e) {
        handleGenericError(e);
        return null;
      }
    },
    [handleGenericError, generateSecretKeyMutation],
  );

  return { isLoading: loading, generateSecretKey };
}

export const useMerchantInvite = (inviteHash: string) => {
  return useMerchantInviteQuery({
    variables: { inviteHash },
  });
};

export function useInviteHash(): string {
  const { inviteHash } = useParams<{ inviteHash: string }>();

  if (typeof inviteHash === 'string') {
    return inviteHash;
  }

  throw new Error('Failed to parse inviteHash');
}

export const usePricing = (keys: Array<string>) => {
  const result = usePricingQuery({
    variables: { keys },
  });
  return result.data?.pricing;
};
