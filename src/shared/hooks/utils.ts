import type { ReactiveVar } from '@apollo/client';
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { useShowMessage } from './alerts';
import { GlobalStateContext } from './state';

export function useDebouncedValue<T>(value: T, timeout = 500): T {
  const [cachedValue, setCachedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setCachedValue(value);
    }, timeout);

    return () => {
      clearTimeout(timer);
    };
  }, [value, timeout]);

  return cachedValue;
}

export const useMountAwareState = <S>(
  initialState: S | (() => S),
): [S, Dispatch<SetStateAction<S>>] => {
  const [state, setState] = useState<S>(initialState);
  const isMounted = useRef(true);

  useEffect(
    () => () => {
      isMounted.current = false;
    },
    [],
  );

  return [
    state,
    useCallback((value: SetStateAction<S>) => {
      if (isMounted.current) {
        setState(value);
      }
    }, []),
  ];
};

type FromattedAmountConfig = {
  currency?: string;
  fractionDigits?: number;
};
export const useFormattedAmount = (
  amount: number,
  config: FromattedAmountConfig = {},
): string => {
  const { currency = '€', fractionDigits = 2 } = config;
  const { i18n } = useTranslation();

  return (
    amount.toLocaleString(i18n.language, {
      maximumFractionDigits: fractionDigits,
      minimumFractionDigits: fractionDigits,
    }) + currency
  );
};

export const useGetPages = (page: number): { pages: Array<number> } =>
  useMemo(() => {
    const pages = [];

    for (let i = 1; i <= page; i++) {
      pages.push(i);
    }

    return { pages };
  }, [page]);

export const useSetDefaultTableState = <T>(
  tableState: ReactiveVar<T>,
  defaultTableState: T,
): void => {
  const { merchantId } = useContext(GlobalStateContext);
  const isInitialSkipped = useRef(false);

  useEffect(() => {
    if (!isInitialSkipped.current) {
      isInitialSkipped.current = true;
      return;
    }

    tableState(defaultTableState);
  }, [merchantId]);
};

type CopyToClipBoardFunction = (
  stringToBeCopied: string,
  message?: string,
) => void;

export const useCopyToClipboard = (): CopyToClipBoardFunction => {
  const showMessage = useShowMessage();

  return useCallback(
    async (stringToBeCopied: string, message?: string) => {
      await navigator.clipboard.writeText(stringToBeCopied);

      if (message) {
        showMessage(message);
      }
    },
    [showMessage],
  );
};

type Timer = ReturnType<typeof setTimeout>;
type SomeFunction = (...args: Array<unknown>) => void;
/**
 *
 * @param func The original, non debounced function (You can pass any number of args to it)
 * @param delay The delay (in ms) for the function to return
 * @returns The debounced function, which will run only if the debounced function has not been called in the last (delay) ms
 */

export function useDebounce<Func extends SomeFunction>(
  func: Func,
  delay = 1000,
) {
  const [timer, setTimer] = useState<Timer>();

  return ((...args) => {
    const newTimer = setTimeout(() => {
      func(...args);
    }, delay);
    clearTimeout(timer);
    setTimer(newTimer);
  }) as Func;
}
