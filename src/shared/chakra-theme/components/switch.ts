import { ColorSchemes } from '../foundations/colors';

function baseStyleTrack(props: { colorScheme: string }) {
  const { colorScheme: c } = props;

  return {
    borderRadius: 'full',
    p: '0',
    transition: 'all 120ms',
    bg: 'neutral.200',
    _focus: {
      boxShadow: 'none',
    },
    _disabled: {
      opacity: 1,
      bg: 'neutral.100',
      cursor: 'not-allowed',
    },

    _checked: {
      bg: `${c}.600`,
      _hover: {
        bg: `${c}.500`,
      },
      _disabled: {
        bg: 'primary.200',
      },
    },

    _hover: {
      bg: 'neutral.300',
    },
  };
}

const baseStyle = (props: { colorScheme: string }) => ({
  track: baseStyleTrack(props),
});

const sizes = {
  md: {
    track: { w: '2.5rem', h: '1.375rem' },

    thumb: {
      w: '1rem',
      h: '1rem',
      transform: 'translate(0.1875rem, 0.1875rem)',

      _checked: {
        transform: 'translate(1.3125rem, 0.1875rem)',
      },
    },
  },
};

const defaultProps = {
  size: 'md',
  colorScheme: ColorSchemes.PRIMARY,
};

export default {
  baseStyle,
  sizes,
  defaultProps,
};
