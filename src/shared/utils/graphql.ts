import type { GraphQLError } from 'graphql';

function isObject(obj: unknown): obj is Record<string, unknown> {
  return typeof obj === 'object' && obj !== null;
}

function hasGraphQLErrors(
  error: unknown,
): error is { graphQLErrors: ReadonlyArray<GraphQLError> } {
  return (
    isObject(error) &&
    Array.isArray(
      (error as { graphQLErrors: ReadonlyArray<GraphQLError> }).graphQLErrors,
    )
  );
}

// Extracts errors, and returns error without e
export function extractGraphqlErrors(
  error: unknown,
): [ReadonlyArray<GraphQLError>, unknown] {
  if (hasGraphQLErrors(error)) {
    const newError = { ...error };
    delete (newError as Record<string, unknown>).graphQLErrors;
    return [error.graphQLErrors, newError];
  }

  return [[], error];
}

export type ValidationErrors = Record<string, Array<string>>;

export function extractValidationErrors(
  error: unknown,
): [ValidationErrors, unknown] {
  const [graphqlErrors, newError] = extractGraphqlErrors(error);
  if (graphqlErrors.length > 0) {
    if (isObject(newError)) {
      newError.graphQLErrors = graphqlErrors.filter(
        (e) => e.message !== 'validation',
      );
    }

    const validationErrors =
      (graphqlErrors.find((e) => e.message === 'validation')?.extensions
        .validation as ValidationErrors) ?? {};
    return [validationErrors, newError];
  }

  return [{}, newError];
}

export function isUnauthorizedError(error: GraphQLError): boolean {
  return error.message === 'Unauthorized';
}
