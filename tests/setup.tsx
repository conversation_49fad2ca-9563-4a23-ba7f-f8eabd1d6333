import '@testing-library/jest-dom/vitest';

import type { Link } from '@tanstack/react-router';
import { cleanup } from '@testing-library/react';
import { afterEach, beforeAll } from 'vitest';

beforeAll(() => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
});

vi.mock('react-i18next', () => ({
  // this mock makes sure any components using the translate hook can use it without a warning being shown
  useTranslation: () => {
    return {
      t: (str: string) => str,
      i18n: {
        changeLanguage: () => new Promise(() => {}),
      },
    };
  },
  initReactI18next: {
    type: '3rdParty',
    init: () => {},
  },
  Trans: ({ i18nKey }: { i18nKey: string }) => i18nKey,
}));

vi.mock('@tanstack/react-router', () => ({
  useNavigate: () => vi.fn(),
  getRouteApi: () => ({
    useSearch: () => ({ referenceKey: 'mock-reference-key' }),
  }),
  Link: ({ children, to, ...props }: React.ComponentProps<typeof Link>) => (
    <a
      // @ts-expect-error need update type for anchor tag
      data-status={props['data-status']}
      // @ts-expect-error need update type for anchor tag
      data-test-is-external={props['data-test-is-external']}
      // @ts-expect-error need update type for anchor tag
      data-testid={props['data-testid']}
      href={to}
    >
      {children as React.ReactElement}
    </a>
  ),
}));

vi.mock('effector-react', () => ({
  useUnit: vi.fn(() => false),
  Provider: ({ children }: { children: React.ReactNode }) => children,
}));

vi.mock('react-use', () => ({
  useLockBodyScroll: vi.fn(),
  useMedia: vi.fn(() => false),
}));

vi.mock('@entities/invoices', () => ({
  UserInvoiceState: {
    OVERDUE: 'OVERDUE',
  },
  useUserInvoiceNotification: () => ({
    data: null,
  }),
}));

afterEach(() => {
  cleanup();
  vi.restoreAllMocks();
});
