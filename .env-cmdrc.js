const REACT_APP_SENTRY_DSN =
  'https://<EMAIL>/5795024';

const LOGIN_METHODS = {
  MobileId: 'mobile-id',
  SmartId: 'smart-id',
  IdCard: 'id-card',
  Password: 'password',
};
// Google Analytics Containers ID for each country
const GA_CONTAINER_IDS = {
  Et: 'G-BP9BQ3WHTL',
  Lv: 'G-YKEPB1LJ7D',
  Lt: 'G-XKNQEMPCC8',
};

const CONTACTS = {
  REACT_APP_INFO_EMAIL: '<EMAIL>',
  REACT_APP_INFO_PHONE: '(+372) 622 52 52',
  REACT_APP_PARTNER_EMAIL: '<EMAIL>',
  REACT_APP_PARTNER_PHONE: '(+*************',
};

const DEKKER_CONFIG = {
  REACT_APP_SENTRY_DSN,
  ...CONTACTS,
  REACT_APP_REGION: 'et',
  REACT_APP_ENV: 'dekker',
  REACT_APP_LANGUAGES: ['et', 'en', 'ru'],
  REACT_APP_API_ENDPOINT: 'https://api.dekker.ee/graphql',
  REACT_APP_API_URL: 'https://api.dekker.ee/',
  REACT_APP_PARTNER_V1_URL: 'https://partner.dekker.ee',
  REACT_APP_ID_URL: 'https://id.dekker.ee',
  REACT_APP_AVAILABLE_IDENTITY_DOCUMENTS: [
    'ID_CARD',
    'DRIVING_LICENSE',
    'PASSPORT',
  ],
  REACT_APP_AWS_REGION: 'eu-west-2',
  REACT_APP_IS_ELIGIBILITY_STATUS_TOOLTIP_ENABLED: true,
};

// eslint-disable-next-line no-undef
module.exports = {
  test: {
    ...DEKKER_CONFIG,
    REACT_APP_ENV: 'test',
    REACT_APP_SENTRY_DSN: undefined,
  },
  local: {
    ...DEKKER_CONFIG,
    REACT_APP_API_ENDPOINT: 'https://api.esto.test/graphql',
    REACT_APP_API_URL: 'https://api.esto.test/',
    REACT_APP_SENTRY_DSN: undefined,
    REACT_APP_ENV: 'local',
    REACT_APP_LOGIN_METHODS: [
      LOGIN_METHODS.MobileId,
      LOGIN_METHODS.IdCard,
      LOGIN_METHODS.SmartId,
      LOGIN_METHODS.Password,
    ],
  },
  dekker: {
    ...DEKKER_CONFIG,
    REACT_APP_PHONE_PREFIX: '+372',
    REACT_APP_LOGIN_METHODS: [
      LOGIN_METHODS.MobileId,
      LOGIN_METHODS.IdCard,
      LOGIN_METHODS.SmartId,
      LOGIN_METHODS.Password,
    ],
  },
  'dekker-lt': {
    ...DEKKER_CONFIG,
    REACT_APP_ENV: 'dekker-lt',
    REACT_APP_REGION: 'lt',
    REACT_APP_API_ENDPOINT: 'https://api.dekker.lt/graphql',
    REACT_APP_PARTNER_V1_URL: 'https://partner.dekker.lt',
    REACT_APP_ID_URL: 'https://id.dekker.lt/',
    REACT_APP_API_URL: 'https://api.dekker.lt/',
    REACT_APP_PHONE_PREFIX: '+370',
    REACT_APP_LANGUAGES: ['lt', 'en'],
    REACT_APP_INFO_EMAIL: '<EMAIL>',
    REACT_APP_INFO_PHONE: '(+370) 524 09 988',
    REACT_APP_PARTNER_EMAIL: '<EMAIL>',
    REACT_APP_PARTNER_PHONE: '(+370) 524 09 988',
    REACT_APP_LOGIN_METHODS: [
      LOGIN_METHODS.MobileId,
      LOGIN_METHODS.SmartId,
      LOGIN_METHODS.Password,
    ],
  },
  'dekker-lv': {
    ...DEKKER_CONFIG,
    REACT_APP_ENV: 'dekker-lv',
    REACT_APP_REGION: 'lv',
    REACT_APP_API_ENDPOINT: 'https://api.dekker.lv/graphql',
    REACT_APP_PARTNER_V1_URL: 'https://partner.dekker.lv',
    REACT_APP_ID_URL: 'https://id.dekker.lv/',
    REACT_APP_API_URL: 'https://api.dekker.lv/',
    REACT_APP_PHONE_PREFIX: '+370',
    REACT_APP_LANGUAGES: ['lv', 'en'],
    REACT_APP_INFO_EMAIL: '<EMAIL>',
    REACT_APP_INFO_PHONE: '(+371) 662 222 50',
    REACT_APP_PARTNER_EMAIL: '<EMAIL>',
    REACT_APP_PARTNER_PHONE: '(+371) 2 7299292',
    REACT_APP_PARTNER_PHONE_LOGGED_IN: '(+371) 2 7299292',
    REACT_APP_LOGIN_METHODS: [
      LOGIN_METHODS.MobileId,
      LOGIN_METHODS.SmartId,
      LOGIN_METHODS.Password,
    ],
  },
  'dekker-dev0': {
    ...DEKKER_CONFIG,
    REACT_APP_ENV: 'dekker-dev0',
    REACT_APP_PHONE_PREFIX: '+372',
    REACT_APP_API_ENDPOINT: 'https://api.dekker-dev0.ee/graphql',
    REACT_APP_PARTNER_V1_URL: 'https://partner.dekker-dev0.ee',
    REACT_APP_ID_URL: 'https://id.dekker-dev0.ee/',
    REACT_APP_API_URL: 'https://api.dekker-dev0.ee/',
    REACT_APP_LOGIN_METHODS: [LOGIN_METHODS.MobileId, LOGIN_METHODS.Password],
  },
  'dekker-dev1': {
    ...DEKKER_CONFIG,
    REACT_APP_ENV: 'dekker-dev1',
    REACT_APP_PHONE_PREFIX: '+372',
    REACT_APP_SENTRY_DSN: undefined,
    REACT_APP_LANGUAGES: ['et', 'en', 'ru', 'lt', 'lv'],
    REACT_APP_API_ENDPOINT: 'https://api.dekker-dev1.ee/graphql',
    REACT_APP_PARTNER_V1_URL: 'https://partner.dekker-dev1.ee',
    REACT_APP_ID_URL: 'https://id.dekker-dev1.ee',
    REACT_APP_API_URL: 'https://api.dekker-dev1.ee/',
    REACT_APP_LOGIN_METHODS: [LOGIN_METHODS.MobileId, LOGIN_METHODS.Password],
  },
  'dekker-dev2': {
    ...DEKKER_CONFIG,
    REACT_APP_ENV: 'dekker-dev2',
    REACT_APP_PHONE_PREFIX: '+372',
    REACT_APP_LANGUAGES: ['et', 'en', 'ru', 'lt', 'lv'],
    REACT_APP_SENTRY_DSN: undefined,
    REACT_APP_API_ENDPOINT: 'https://api.dekker-dev2.ee/graphql',
    REACT_APP_PARTNER_V1_URL: 'https://partner.dekker-dev2.ee',
    REACT_APP_ID_URL: 'https://id.dekker-dev2.ee',
    REACT_APP_API_URL: 'https://api.dekker-dev2.ee/',
    REACT_APP_LOGIN_METHODS: [LOGIN_METHODS.MobileId, LOGIN_METHODS.Password],
  },
  'dekker-dev3': {
    ...DEKKER_CONFIG,
    REACT_APP_ENV: 'dekker-dev3',
    REACT_APP_PHONE_PREFIX: '+372',
    REACT_APP_SENTRY_DSN: undefined,
    REACT_APP_API_ENDPOINT: 'https://api.dekker-dev3.ee/graphql',
    REACT_APP_PARTNER_V1_URL: 'https://partner.dekker-dev3.ee',
    REACT_APP_ID_URL: 'https://id.dekker-dev3.ee',
    REACT_APP_API_URL: 'https://api.dekker-dev3.ee/',
    REACT_APP_LOGIN_METHODS: [
      LOGIN_METHODS.MobileId,
      LOGIN_METHODS.IdCard,
      LOGIN_METHODS.SmartId,
      LOGIN_METHODS.Password,
    ],
  },
  'prod-et': {
    REACT_APP_SENTRY_DSN,
    ...CONTACTS,
    REACT_APP_ENV: 'prod-et',
    REACT_APP_REGION: 'et',
    REACT_APP_LANGUAGES: ['et', 'en', 'ru'],
    REACT_APP_API_ENDPOINT: 'https://api.esto.ee/graphql',
    REACT_APP_API_URL: 'https://api.esto.ee/',
    REACT_APP_LOGIN_METHODS: [
      LOGIN_METHODS.MobileId,
      LOGIN_METHODS.SmartId,
      LOGIN_METHODS.IdCard,
      LOGIN_METHODS.Password,
    ],
    REACT_APP_PARTNER_V1_URL: 'https://partner.esto.ee',
    REACT_APP_PHONE_PREFIX: '+372',
    REACT_APP_ID_URL: 'https://id.esto.ee',
    REACT_APP_GA_ID: 'G-BP9BQ3WHTL',
    REACT_APP_AVAILABLE_IDENTITY_DOCUMENTS: [
      'ID_CARD',
      'DRIVING_LICENSE',
      'PASSPORT',
    ],
    REACT_APP_AWS_REGION: 'eu-west-2',
    REACT_APP_GA_CONTAINER_ID: GA_CONTAINER_IDS.Et,
    REACT_APP_ZENDESK_CHAT_KEY_CODE: '95655457-0b07-4324-ac33-c72c8d07cf68',
    REACT_APP_IS_ELIGIBILITY_STATUS_TOOLTIP_ENABLED: false,
    REACT_APP_POSTHOG_KEY: 'phc_5IMpqQRAqWDOIJwS60oJXQXB115C4iBRPZBPlmyfJM0',
    REACT_APP_POSTHOG_HOST: 'https://eu.i.posthog.com',
  },
  'prod-lt': {
    REACT_APP_SENTRY_DSN,
    REACT_APP_ENV: 'prod-lt',
    REACT_APP_REGION: 'lt',
    REACT_APP_INFO_EMAIL: '<EMAIL>',
    REACT_APP_INFO_PHONE: '(+370) 524 09 988',
    REACT_APP_PARTNER_EMAIL: '<EMAIL>',
    REACT_APP_PARTNER_PHONE: '(+370) 669 10 701',
    REACT_APP_LANGUAGES: ['lt', 'en', 'ru'],
    REACT_APP_API_ENDPOINT: 'https://api.estopay.lt/graphql',
    REACT_APP_API_URL: 'https://api.estopay.lt/',
    REACT_APP_LOGIN_METHODS: [
      LOGIN_METHODS.MobileId,
      LOGIN_METHODS.SmartId,
      LOGIN_METHODS.Password,
    ],
    REACT_APP_PARTNER_V1_URL: 'https://partner.estopay.lt',
    REACT_APP_PHONE_PREFIX: '+370',
    REACT_APP_ID_URL: 'https://id.estopay.lt',
    REACT_APP_GA_ID: 'G-XKNQEMPCC8',
    REACT_APP_AVAILABLE_IDENTITY_DOCUMENTS: [
      'ID_CARD',
      'DRIVING_LICENSE',
      'PASSPORT',
    ],
    REACT_APP_AWS_REGION: 'eu-west-2',
    REACT_APP_GA_CONTAINER_ID: GA_CONTAINER_IDS.Lt,
    REACT_APP_ZENDESK_CHAT_KEY_CODE: '7c08a1b6-1bf8-4ef9-9e64-230c81a41ba2',
    REACT_APP_IS_ELIGIBILITY_STATUS_TOOLTIP_ENABLED: true,
    REACT_APP_POSTHOG_KEY: 'phc_vU210kf8c7re58ovGnGY39FoQ7Y94FMhoiJ204NexdJ',
    REACT_APP_POSTHOG_HOST: 'https://eu.i.posthog.com',
  },
  'prod-lv': {
    REACT_APP_SENTRY_DSN,
    REACT_APP_ENV: 'prod-lv',
    REACT_APP_REGION: 'lv',
    REACT_APP_INFO_EMAIL: '<EMAIL>',
    REACT_APP_INFO_PHONE: '(+371) 662 222 50',
    REACT_APP_PARTNER_EMAIL: '<EMAIL>',
    REACT_APP_PARTNER_PHONE: '(+371) 2 7299292',
    REACT_APP_PARTNER_PHONE_LOGGED_IN: '(+371) 27299292',
    REACT_APP_LANGUAGES: ['lv', 'en', 'ru'],
    REACT_APP_API_ENDPOINT: 'https://api.esto.lv/graphql',
    REACT_APP_API_URL: 'https://api.esto.lv/',
    REACT_APP_LOGIN_METHODS: [
      LOGIN_METHODS.MobileId,
      LOGIN_METHODS.SmartId,
      LOGIN_METHODS.Password,
    ],
    REACT_APP_PARTNER_V1_URL: 'https://partner.esto.lv',
    REACT_APP_PHONE_PREFIX: '+371',
    REACT_APP_ID_URL: 'https://id.esto.lv',
    REACT_APP_GA_ID: 'G-JPP9LCRNBL',
    REACT_APP_AVAILABLE_IDENTITY_DOCUMENTS: [
      'ID_CARD',
      'DRIVING_LICENSE',
      'PASSPORT',
    ],
    REACT_APP_AWS_REGION: 'eu-west-2',
    REACT_APP_GA_CONTAINER_ID: GA_CONTAINER_IDS.Lv,
    REACT_APP_ZENDESK_CHAT_KEY_CODE: '2e603a54-1936-4e31-8d56-9f7ccd1e4e98',
    REACT_APP_IS_ELIGIBILITY_STATUS_TOOLTIP_ENABLED: false,
    REACT_APP_POSTHOG_KEY: 'phc_Aehj7YBFzJ11zzW0WGa9pjB4EDxyIo9JaQPTcTjszAK',
    REACT_APP_POSTHOG_HOST: 'https://eu.i.posthog.com',
  },
};
